/**
 * Syrian Identity Button Component
 * 
 * A production-ready button component with:
 * - RTL-first design using logical properties
 * - Syrian cultural color variants
 * - Full accessibility support (WCAG AA)
 * - Arabic typography optimization
 * - Touch-friendly sizing
 * - Loading and disabled states
 * - Icon support with proper spacing
 */

import * as React from 'react';
import { clsx } from 'clsx';

export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
export type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * Visual style variant based on Syrian design language.
   * @default 'primary'
   */
  variant?: ButtonVariant;
  
  /**
   * Size scale for different use cases.
   * @default 'md'
   */
  size?: ButtonSize;
  
  /**
   * Icon to display at the start of the button (respects RTL).
   */
  startIcon?: React.ReactNode;
  
  /**
   * Icon to display at the end of the button (respects RTL).
   */
  endIcon?: React.ReactNode;
  
  /**
   * Whether the button should take full width of its container.
   * @default false
   */
  fullWidth?: boolean;
  
  /**
   * Loading state - shows spinner and disables interaction.
   * @default false
   */
  loading?: boolean;
  
  /**
   * Text direction override. Usually auto-detected from content.
   * @default 'auto'
   */
  dir?: 'rtl' | 'ltr' | 'auto';
  
  /**
   * Button content - can be Arabic or English text.
   */
  children: React.ReactNode;
}

/**
 * Loading spinner component
 */
const LoadingSpinner: React.FC = () => (
  <span
    style={{
      display: 'inline-block',
      width: '1em',
      height: '1em',
      border: '2px solid transparent',
      borderTopColor: 'currentColor',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite'
    }}
    aria-hidden="true"
  />
);

/**
 * Syrian Identity Button Component
 * 
 * @example
 * ```tsx
 * // Arabic primary button
 * <Button variant="primary">متابعة</Button>
 * 
 * // English secondary button with icon
 * <Button variant="secondary" startIcon={<CheckIcon />} dir="ltr">
 *   Continue
 * </Button>
 * 
 * // Loading state
 * <Button loading>جارٍ التحميل...</Button>
 * ```
 */
export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      startIcon,
      endIcon,
      fullWidth = false,
      loading = false,
      disabled = false,
      dir = 'auto',
      className,
      children,
      type = 'button',
      ...rest
    },
    ref
  ) => {
    // Determine if button has icons for spacing
    const hasIcons = Boolean(startIcon || endIcon || loading);

    // Compute final disabled state
    const isDisabled = disabled || loading;

    // Get variant styles
    const getVariantStyles = (): React.CSSProperties => {
      const baseStyles: React.CSSProperties = {
        fontFamily: 'var(--sid-font-arabic)',
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        paddingBlock: 'var(--sid-space-2)',
        paddingInline: 'var(--sid-space-4)',
        border: '1px solid transparent',
        borderRadius: 'var(--sid-radius-button)',
        fontSize: 'var(--sid-text-base)',
        fontWeight: 'var(--sid-font-medium)',
        lineHeight: 'var(--sid-leading-normal)',
        textDecoration: 'none',
        whiteSpace: 'nowrap',
        inlineSize: fullWidth ? '100%' : 'auto',
        minBlockSize: 'var(--sid-space-44)',
        cursor: isDisabled ? 'not-allowed' : 'pointer',
        userSelect: 'none',
        transition: 'background-color 150ms ease, border-color 150ms ease, color 150ms ease, box-shadow 150ms ease, transform 100ms ease',
        boxShadow: 'var(--sid-shadow-button)',
        opacity: isDisabled ? 0.5 : 1,
        gap: hasIcons ? 'var(--sid-space-2)' : undefined
      };

      switch (variant) {
        case 'primary':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-forest-600)',
            color: 'var(--sid-text-on-color)',
            borderColor: 'var(--sid-forest-600)'
          };
        case 'secondary':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-wheat-400)',
            color: 'var(--sid-charcoal-900)',
            borderColor: 'var(--sid-wheat-400)'
          };
        case 'outline':
          return {
            ...baseStyles,
            backgroundColor: 'transparent',
            color: 'var(--sid-forest-600)',
            borderColor: 'var(--sid-forest-600)'
          };
        case 'ghost':
          return {
            ...baseStyles,
            backgroundColor: 'transparent',
            color: 'var(--sid-forest-600)',
            borderColor: 'transparent',
            boxShadow: 'none'
          };
        case 'destructive':
          return {
            ...baseStyles,
            backgroundColor: 'var(--sid-flag-red)',
            color: 'var(--sid-text-on-color)',
            borderColor: 'var(--sid-flag-red)'
          };
        default:
          return baseStyles;
      }
    };
    
    return (
      <button
        ref={ref}
        type={type}
        dir={dir}
        disabled={isDisabled}
        aria-disabled={isDisabled || undefined}
        aria-busy={loading || undefined}
        style={getVariantStyles()}
        className={className}
        {...rest}
      >
        {/* Start icon or loading spinner */}
        {loading ? (
          <LoadingSpinner />
        ) : startIcon ? (
          <span style={{ inlineSize: '1em', blockSize: '1em', flexShrink: 0 }} aria-hidden="true">
            {startIcon}
          </span>
        ) : null}

        {/* Button text content */}
        {children}

        {/* End icon (only if not loading) */}
        {!loading && endIcon && (
          <span style={{ inlineSize: '1em', blockSize: '1em', flexShrink: 0 }} aria-hidden="true">
            {endIcon}
          </span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';
