{"name": "@sid/components", "version": "0.1.0", "description": "Syrian Identity UI Components - RTL-first React components with cultural authenticity", "sideEffects": false, "license": "MIT", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./button": {"types": "./dist/button.d.ts", "import": "./dist/button.mjs", "require": "./dist/button.js"}, "./input": {"types": "./dist/input.d.ts", "import": "./dist/input.mjs", "require": "./dist/input.js"}, "./card": {"types": "./dist/card.d.ts", "import": "./dist/card.mjs", "require": "./dist/card.js"}, "./styles": {"import": "./dist/styles.css"}}, "files": ["dist/**"], "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint . --max-warnings 0", "clean": "rm -rf .turbo node_modules dist", "type-check": "tsc --noEmit"}, "dependencies": {"@sid/tokens": "workspace:*", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "framer-motion": "^10.16.16", "clsx": "^2.0.0"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "eslint": "^8.57.0", "postcss": "^8.4.32", "postcss-cli": "^11.0.0", "tsup": "^8.0.2", "typescript": "5.5.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "publishConfig": {"access": "public"}, "keywords": ["react", "components", "syrian", "rtl", "arabic", "design-system", "accessibility", "cultural-design"]}