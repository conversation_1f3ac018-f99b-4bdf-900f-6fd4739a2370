{"name": "@sid/ui-legacy", "version": "0.0.0", "description": "Legacy UI package - being migrated to @sid/components", "sideEffects": false, "license": "MIT", "private": true, "exports": {"./button": {"types": "./src/button.tsx", "import": "./dist/button.mjs", "require": "./dist/button.js"}}, "scripts": {"build": "echo 'Legacy package - use @sid/components instead'", "dev": "echo 'Legacy package - use @sid/components instead'", "lint": "eslint . --max-warnings 0", "clean": "rm -rf .turbo node_modules dist"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "eslint": "^8.57.0", "@repo/typescript-config": "workspace:*", "tsup": "^8.0.2", "typescript": "5.5.4"}, "dependencies": {"react": "^18.2.0"}}