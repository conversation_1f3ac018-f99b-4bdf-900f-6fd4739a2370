/**
 * Syrian Identity Button - Storybook Stories
 *
 * Comprehensive examples showcasing:
 * - Arabic and English text
 * - RTL/LTR support
 * - All variants and sizes
 * - Accessibility features
 * - Cultural context
 */

import type { Meta, StoryObj } from '@storybook/react';
import { <PERSON><PERSON> } from '@sid/components';

// Mock icons for examples
const ChevronRightIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M6 12l4-4-4-4v8z"/>
  </svg>
);

const CheckIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M13.5 3.5L6 11 2.5 7.5l1-1L6 9l6.5-6.5 1 1z"/>
  </svg>
);

const HeartIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M8 14s-6-4.5-6-8.5C2 3.5 4 2 6 2c1 0 2 .5 2 1.5C8 2.5 9 2 10 2c2 0 4 1.5 4 3.5C14 9.5 8 14 8 14z"/>
  </svg>
);

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
# Syrian Identity Button

A production-ready button component designed for Syrian cultural context with RTL-first approach.

## Features
- **RTL/LTR Support**: Uses CSS logical properties for seamless direction switching
- **Syrian Color Palette**: Forest green, golden wheat, and cultural colors
- **Arabic Typography**: Optimized for Arabic text rendering with proper fallbacks
- **Accessibility**: WCAG AA compliant with proper focus management
- **Touch Friendly**: Minimum 44px touch targets on all sizes
- **Loading States**: Built-in spinner with proper ARIA attributes

## Cultural Context
The button variants are inspired by Syrian visual identity:
- **Primary**: Syrian forest green (olive groves and landscapes)
- **Secondary**: Golden wheat (agricultural heritage)
- **Destructive**: Syrian flag red for critical actions
        `
      }
    }
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline', 'ghost', 'destructive'],
      description: 'Visual style variant based on Syrian design language'
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl'],
      description: 'Size scale for different use cases'
    },
    loading: {
      control: 'boolean',
      description: 'Shows loading spinner and disables interaction'
    },
    disabled: {
      control: 'boolean',
      description: 'Disables the button'
    },
    fullWidth: {
      control: 'boolean',
      description: 'Makes button take full width of container'
    },
    dir: {
      control: 'select',
      options: ['auto', 'rtl', 'ltr'],
      description: 'Text direction override'
    }
  },
  args: {
    children: 'زر سوري',
    variant: 'primary',
    size: 'md',
    dir: 'auto'
  }
};

export default meta;
type Story = StoryObj<typeof Button>;

// === BASIC EXAMPLES ===

export const Arabic: Story = {
  args: {
    children: 'متابعة',
    variant: 'primary'
  }
};

export const English: Story = {
  args: {
    children: 'Continue',
    variant: 'primary',
    dir: 'ltr'
  }
};

// === VARIANTS ===

export const AllVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap', alignItems: 'center' }}>
      <Button variant="primary">أساسي</Button>
      <Button variant="secondary">ثانوي</Button>
      <Button variant="outline">مخطط</Button>
      <Button variant="ghost">شبح</Button>
      <Button variant="destructive">حذف</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All button variants showcasing Syrian cultural colors and design language.'
      }
    }
  }
};

export const VariantsEnglish: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap', alignItems: 'center' }} dir="ltr">
      <Button variant="primary" dir="ltr">Primary</Button>
      <Button variant="secondary" dir="ltr">Secondary</Button>
      <Button variant="outline" dir="ltr">Outline</Button>
      <Button variant="ghost" dir="ltr">Ghost</Button>
      <Button variant="destructive" dir="ltr">Destructive</Button>
    </div>
  )
};

// === SIZES ===

export const AllSizes: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', flexWrap: 'wrap' }}>
      <Button size="xs">صغير جداً</Button>
      <Button size="sm">صغير</Button>
      <Button size="md">متوسط</Button>
      <Button size="lg">كبير</Button>
      <Button size="xl">كبير جداً</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All button sizes with Arabic text, showing touch-friendly sizing.'
      }
    }
  }
};

// === WITH ICONS ===

export const WithStartIcon: Story = {
  args: {
    children: 'التالي',
    startIcon: <ChevronRightIcon />,
    variant: 'primary'
  }
};

export const WithEndIcon: Story = {
  args: {
    children: 'تم',
    endIcon: <CheckIcon />,
    variant: 'secondary'
  }
};

export const WithBothIcons: Story = {
  args: {
    children: 'أعجبني',
    startIcon: <HeartIcon />,
    endIcon: <ChevronRightIcon />,
    variant: 'outline'
  }
};

// === STATES ===

export const LoadingState: Story = {
  args: {
    children: 'جارٍ التحميل...',
    loading: true,
    variant: 'primary'
  }
};

export const DisabledState: Story = {
  args: {
    children: 'غير متاح',
    disabled: true,
    variant: 'primary'
  }
};

export const AllStates: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap', alignItems: 'center' }}>
      <Button variant="primary">عادي</Button>
      <Button variant="primary" loading>جارٍ التحميل</Button>
      <Button variant="primary" disabled>معطل</Button>
    </div>
  )
};

// === LAYOUT ===

export const FullWidth: Story = {
  args: {
    children: 'زر بعرض كامل',
    fullWidth: true,
    variant: 'primary'
  },
  decorators: [
    (Story: any) => (
      <div style={{ width: '300px' }}>
        <Story />
      </div>
    )
  ]
};

// === CULTURAL EXAMPLES ===

export const SyrianGreeting: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', alignItems: 'center' }}>
      <h3 style={{ fontFamily: 'var(--sid-font-arabic)', margin: 0 }}>أهلاً وسهلاً</h3>
      <p style={{ textAlign: 'center', margin: 0 }}>مرحباً بك في مكتبة الهوية السورية للمكونات</p>
      <div style={{ display: 'flex', gap: '0.5rem' }}>
        <Button variant="primary" startIcon={<HeartIcon />}>
          أهلاً بك
        </Button>
        <Button variant="outline">
          تعرف أكثر
        </Button>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Example showing Syrian greeting with cultural context and proper Arabic typography.'
      }
    }
  }
};

export const BilingualInterface: Story = {
  render: () => (
    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '2rem', alignItems: 'start' }}>
      {/* Arabic Side */}
      <div dir="rtl" style={{ textAlign: 'start' }}>
        <h4 style={{ fontFamily: 'var(--sid-font-arabic)', margin: '0 0 1rem 0' }}>النسخة العربية</h4>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          <Button variant="primary" fullWidth>حفظ التغييرات</Button>
          <Button variant="outline" fullWidth>إلغاء</Button>
        </div>
      </div>

      {/* English Side */}
      <div dir="ltr" style={{ textAlign: 'start' }}>
        <h4 style={{ fontFamily: 'var(--sid-font-latin)', margin: '0 0 1rem 0' }}>English Version</h4>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          <Button variant="primary" fullWidth dir="ltr">Save Changes</Button>
          <Button variant="outline" fullWidth dir="ltr">Cancel</Button>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Bilingual interface example showing RTL/LTR support with proper typography for each language.'
      }
    }
  }
};
